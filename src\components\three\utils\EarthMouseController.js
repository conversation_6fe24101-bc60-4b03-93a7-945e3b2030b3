import * as THREE from "three";

/**
 * 地球原生鼠标控制器
 * 替代OrbitControls，使用原生鼠标事件实现地球旋转功能
 */
class EarthMouseController {
  constructor(camera, renderer, earthGroup, elementsGroup = null) {
    console.log("EarthMouseController constructor called");

    try {
      this.camera = camera;
      this.renderer = renderer;
      this.earthGroup = earthGroup;
      this.elementsGroup = elementsGroup || earthGroup; // 如果没有提供elementsGroup，则使用earthGroup
      this.domElement = renderer.domElement;

      // 目标位置（兼容OrbitControls接口）
      this.target = new THREE.Vector3(0, -1, -2.7);
      console.log("Target initialized:", this.target);

      // 鼠标状态
      this.isMouseDown = false;
      this.mouseX = 0;
      this.mouseY = 0;
      this.lastMouseX = 0;
      this.lastMouseY = 0;

      // 旋转参数
      this.rotationSpeed = 0.005; // 旋转速度
      this.dampingFactor = 0.95; // 阻尼系数
      this.autoRotateSpeed = 0.001; // 自动旋转速度
      this.enableAutoRotate = false; // 是否启用自动旋转

      // 当前旋转速度（用于惯性）
      this.currentRotationX = 0;
      this.currentRotationY = 0;

      // 缩放参数
      this.enableZoom = true; // 是否启用缩放
      this.zoomSpeed = 0.1; // 缩放速度
      this.minDistance = 50; // 最小距离
      this.maxDistance = 100; // 最大距离
      this.currentZoomVelocity = 0; // 当前缩放速度（用于惯性）
      this.zoomDampingFactor = 0.9; // 缩放阻尼系数

      // 调整相机位置，使其看向目标点（模拟OrbitControls的行为）
      this.adjustCameraToTarget();

      // 绑定事件
      this.bindEvents();

      console.log("EarthMouseController initialized successfully");
      console.log("Zoom enabled:", this.enableZoom, "Min distance:", this.minDistance, "Max distance:", this.maxDistance);
    } catch (error) {
      console.error("Error in EarthMouseController constructor:", error);
      throw error;
    }
  }

  /**
   * 调整相机位置，使其看向目标点（模拟OrbitControls的行为）
   */
  adjustCameraToTarget() {
    // 让相机看向目标点
    this.camera.lookAt(this.target);
    console.log("Camera adjusted to look at target:", this.target);
    console.log("Camera position:", this.camera.position);
  }

  /**
   * 绑定鼠标事件
   */
  bindEvents() {
    this.onMouseDown = this.onMouseDown.bind(this);
    this.onMouseMove = this.onMouseMove.bind(this);
    this.onMouseUp = this.onMouseUp.bind(this);
    this.onContextMenu = this.onContextMenu.bind(this);
    this.onWheel = this.onWheel.bind(this);

    this.domElement.addEventListener("mousedown", this.onMouseDown);
    this.domElement.addEventListener("mousemove", this.onMouseMove);
    this.domElement.addEventListener("mouseup", this.onMouseUp);
    this.domElement.addEventListener("contextmenu", this.onContextMenu);
    this.domElement.addEventListener("wheel", this.onWheel, { passive: false });

    // 防止选择文本
    this.domElement.style.userSelect = "none";
    this.domElement.style.webkitUserSelect = "none";
  }

  /**
   * 鼠标按下事件
   */
  onMouseDown(event) {
    event.preventDefault();

    this.isMouseDown = true;
    this.lastMouseX = event.clientX;
    this.lastMouseY = event.clientY;

    // 重置当前旋转速度
    this.currentRotationX = 0;
    this.currentRotationY = 0;

    this.domElement.style.cursor = "grabbing";
  }

  /**
   * 鼠标移动事件
   */
  onMouseMove(event) {
    event.preventDefault();

    if (!this.isMouseDown) return;

    const deltaX = event.clientX - this.lastMouseX;
    const deltaY = event.clientY - this.lastMouseY;

    // 计算旋转增量
    this.currentRotationX = deltaY * this.rotationSpeed; // 恢复正常垂直方向
    this.currentRotationY = deltaX * this.rotationSpeed;

    // 直接旋转元素组（而不是整个地球组）
    this.elementsGroup.rotation.x += this.currentRotationX;
    this.elementsGroup.rotation.y += this.currentRotationY;

    this.lastMouseX = event.clientX;
    this.lastMouseY = event.clientY;
  }

  /**
   * 鼠标抬起事件
   */
  onMouseUp(event) {
    event.preventDefault();

    this.isMouseDown = false;
    this.domElement.style.cursor = "grab";
  }

  /**
   * 禁用右键菜单
   */
  onContextMenu(event) {
    event.preventDefault();
  }

  /**
   * 鼠标滚轮事件（缩放）
   */
  onWheel(event) {
    if (!this.enableZoom) return;

    event.preventDefault();

    // 获取滚轮方向（标准化处理）
    const delta = event.deltaY > 0 ? 1 : -1;

    // 计算缩放增量
    const zoomDelta = delta * this.zoomSpeed;
    this.currentZoomVelocity += zoomDelta;

    // 立即应用缩放
    this.applyZoom(zoomDelta);
  }

  /**
   * 应用缩放变换
   */
  applyZoom(zoomDelta) {
    // 计算相机到目标点的方向向量
    const direction = new THREE.Vector3();
    direction.subVectors(this.camera.position, this.target).normalize();

    // 计算当前距离
    const currentDistance = this.camera.position.distanceTo(this.target);

    // 计算新的距离
    const newDistance = Math.max(this.minDistance, Math.min(this.maxDistance, currentDistance + zoomDelta));

    // 如果距离没有变化（达到边界），则不进行缩放
    if (Math.abs(newDistance - currentDistance) < 0.001) {
      this.currentZoomVelocity *= 0.5; // 减少速度以避免在边界处震荡
      return;
    }

    // 计算新的相机位置
    const newPosition = new THREE.Vector3();
    newPosition.copy(this.target).add(direction.multiplyScalar(newDistance));

    // 更新相机位置
    this.camera.position.copy(newPosition);

    // 确保相机仍然看向目标点
    this.camera.lookAt(this.target);
  }

  /**
   * 更新控制器（在动画循环中调用）
   */
  update() {
    // 如果没有鼠标交互，应用惯性和自动旋转
    if (!this.isMouseDown) {
      // 应用旋转惯性
      if (Math.abs(this.currentRotationX) > 0.001 || Math.abs(this.currentRotationY) > 0.001) {
        // 继续旋转元素组（而不是整个地球组）
        this.elementsGroup.rotation.x += this.currentRotationX;
        this.elementsGroup.rotation.y += this.currentRotationY;

        // 应用阻尼
        this.currentRotationX *= this.dampingFactor;
        this.currentRotationY *= this.dampingFactor;
      }

      // 应用缩放惯性
      if (this.enableZoom && Math.abs(this.currentZoomVelocity) > 0.001) {
        this.applyZoom(this.currentZoomVelocity);
        this.currentZoomVelocity *= this.zoomDampingFactor;
      }

      // 自动旋转
      if (this.enableAutoRotate) {
        this.elementsGroup.rotation.y += this.autoRotateSpeed;
      }
    }
  }

  /**
   * 设置目标位置（兼容OrbitControls接口）
   */
  setTarget(x, y, z) {
    this.target.set(x, y, z);
    // 当目标改变时，重新调整相机朝向
    this.adjustCameraToTarget();
  }

  /**
   * 启用/禁用自动旋转
   */
  setAutoRotate(enabled) {
    this.enableAutoRotate = enabled;
  }

  /**
   * 设置旋转速度
   */
  setRotationSpeed(speed) {
    this.rotationSpeed = speed;
  }

  /**
   * 设置阻尼系数
   */
  setDampingFactor(factor) {
    this.dampingFactor = factor;
  }

  /**
   * 启用/禁用缩放功能
   */
  setEnableZoom(enabled) {
    this.enableZoom = enabled;
    console.log("Zoom enabled:", this.enableZoom);
  }

  /**
   * 设置缩放速度
   */
  setZoomSpeed(speed) {
    this.zoomSpeed = Math.max(0.01, Math.min(1.0, speed)); // 限制在合理范围内
    console.log("Zoom speed set to:", this.zoomSpeed);
  }

  /**
   * 设置缩放距离限制
   */
  setZoomLimits(minDistance, maxDistance) {
    this.minDistance = Math.max(1, minDistance); // 最小距离不能小于1
    this.maxDistance = Math.max(this.minDistance + 1, maxDistance); // 最大距离必须大于最小距离
    console.log("Zoom limits set - Min:", this.minDistance, "Max:", this.maxDistance);
  }

  /**
   * 设置缩放阻尼系数
   */
  setZoomDampingFactor(factor) {
    this.zoomDampingFactor = Math.max(0.1, Math.min(0.99, factor)); // 限制在合理范围内
    console.log("Zoom damping factor set to:", this.zoomDampingFactor);
  }

  /**
   * 获取当前相机距离目标点的距离
   */
  getCurrentDistance() {
    return this.camera.position.distanceTo(this.target);
  }

  /**
   * 平滑缩放到指定距离
   */
  zoomToDistance(targetDistance, duration = 1000) {
    const currentDistance = this.getCurrentDistance();
    const distance = Math.max(this.minDistance, Math.min(this.maxDistance, targetDistance));

    if (Math.abs(distance - currentDistance) < 0.001) {
      return; // 距离差异太小，不需要动画
    }

    // 使用简单的线性插值实现平滑缩放
    const startTime = Date.now();
    const startDistance = currentDistance;
    const deltaDistance = distance - startDistance;

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 使用easeInOut缓动函数
      const easeProgress = progress < 0.5 ? 2 * progress * progress : 1 - Math.pow(-2 * progress + 2, 2) / 2;

      const currentTargetDistance = startDistance + deltaDistance * easeProgress;
      const direction = new THREE.Vector3();
      direction.subVectors(this.camera.position, this.target).normalize();

      const newPosition = new THREE.Vector3();
      newPosition.copy(this.target).add(direction.multiplyScalar(currentTargetDistance));

      this.camera.position.copy(newPosition);
      this.camera.lookAt(this.target);

      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };

    animate();
  }

  /**
   * 销毁控制器
   */
  dispose() {
    this.domElement.removeEventListener("mousedown", this.onMouseDown);
    this.domElement.removeEventListener("mousemove", this.onMouseMove);
    this.domElement.removeEventListener("mouseup", this.onMouseUp);
    this.domElement.removeEventListener("contextmenu", this.onContextMenu);
    this.domElement.removeEventListener("wheel", this.onWheel);

    // 恢复默认样式
    this.domElement.style.cursor = "default";
    this.domElement.style.userSelect = "auto";
    this.domElement.style.webkitUserSelect = "auto";

    console.log("EarthMouseController disposed");
  }
}

export { EarthMouseController };
export default EarthMouseController;
